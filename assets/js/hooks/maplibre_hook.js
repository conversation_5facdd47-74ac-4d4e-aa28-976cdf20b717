// Import MapLibre GL JS - use require for better esbuild compatibility
const maplibregl = require('maplibre-gl');

// Map style configurations
const MAP_STYLES = {
  standard: {
    name: 'Standard',
    style: 'https://demotiles.maplibre.org/style.json'
  },
  satellite: {
    name: 'Satellite',
    // Using a custom style with satellite imagery
    style: {
      version: 8,
      name: 'Satellite',
      metadata: {},
      sources: {
        'satellite-source': {
          type: 'raster',
          tiles: [
            'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'
          ],
          tileSize: 256,
          attribution: '© Esri, Maxar, Earthstar Geographics, and the GIS User Community'
        }
      },
      layers: [
        {
          id: 'satellite-layer',
          type: 'raster',
          source: 'satellite-source',
          minzoom: 0,
          maxzoom: 22
        }
      ]
      // No glyphs or sprite needed for pure satellite imagery
    }
  },
  hybrid: {
    name: 'Hybrid',
    // Satellite imagery with street labels overlay
    style: {
      version: 8,
      name: 'Hybrid',
      metadata: {},
      sources: {
        'satellite-source': {
          type: 'raster',
          tiles: [
            'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'
          ],
          tileSize: 256,
          attribution: '© Esri, Maxar, Earthstar Geographics, and the GIS User Community'
        },
        'labels-source': {
          type: 'raster',
          tiles: [
            'https://server.arcgisonline.com/ArcGIS/rest/services/Reference/World_Boundaries_and_Places/MapServer/tile/{z}/{y}/{x}'
          ],
          tileSize: 256,
          attribution: '© Esri'
        }
      },
      layers: [
        {
          id: 'satellite-layer',
          type: 'raster',
          source: 'satellite-source',
          minzoom: 0,
          maxzoom: 22
        },
        {
          id: 'labels-layer',
          type: 'raster',
          source: 'labels-source',
          minzoom: 0,
          maxzoom: 22
        }
      ]
      // No glyphs or sprite needed for raster-only layers
    }
  }
};

// Custom Style Switcher Control
class StyleSwitcherControl {
  constructor(hook) {
    this.hook = hook;
  }

  onAdd(map) {
    this.map = map;
    this.container = document.createElement('div');
    this.container.className = 'maplibregl-ctrl maplibregl-ctrl-group style-switcher-control';

    // Create buttons for each style
    Object.keys(MAP_STYLES).forEach(styleKey => {
      const button = document.createElement('button');
      button.className = `style-switcher-btn ${styleKey === this.hook.currentStyle ? 'active' : ''}`;
      button.type = 'button';
      button.title = `Switch to ${MAP_STYLES[styleKey].name} view`;
      button.textContent = MAP_STYLES[styleKey].name;
      button.setAttribute('data-style', styleKey);

      button.addEventListener('click', (e) => {
        e.preventDefault();
        this.switchToStyle(styleKey);
      });

      this.container.appendChild(button);
    });

    return this.container;
  }

  onRemove() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
    this.map = undefined;
  }

  switchToStyle(styleKey) {
    // Update button states
    const buttons = this.container.querySelectorAll('.style-switcher-btn');
    buttons.forEach(btn => {
      btn.classList.toggle('active', btn.getAttribute('data-style') === styleKey);
    });

    // Switch the map style
    this.hook.switchStyle(styleKey);
  }
}

// Define the MapLibre hook as a separate object to ensure proper registration
const MapLibreHook = {
  mounted() {
    console.log('MapLibre hook mounted successfully!');

    // Prevent double initialization
    if (this.map) {
      console.log('Map already initialized, skipping...');
      return;
    }

    // Initialize current style from data attribute or default to standard
    this.currentStyle = this.el.dataset.styleType || 'standard';

    // Show loading message
    this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #e8f5e8; border: 2px solid #4caf50; color: #2e7d32; font-family: Arial, sans-serif; font-size: 18px;">✅ MapLibre Hook Loaded!<br><small>Initializing map...</small></div>';

    // Initialize map immediately (no delay needed)
    this.initializeMap();
  },

  initializeMap() {
    try {
      // Prevent double initialization
      if (this.map) {
        console.log('Map already exists, skipping initialization...');
        return;
      }

      console.log('Initializing MapLibre map...');
      console.log('MapLibre GL object:', maplibregl);
      console.log('MapLibre GL keys:', Object.keys(maplibregl));

      // Try to get version from different possible locations
      const version = maplibregl.version || maplibregl.getVersion?.() || 'unknown';
      console.log('MapLibre GL version:', version);

      // Get configuration from data attributes
      const center = JSON.parse(this.el.dataset.center);
      const zoom = parseFloat(this.el.dataset.zoom);

      console.log('Map config:', { center, zoom });

      // Check if MapLibre GL is supported (basic WebGL check)
      if (typeof maplibregl.supported === 'function') {
        if (!maplibregl.supported()) {
          console.error('MapLibre GL is not supported in this browser');
          this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #ffebee; border: 2px solid #f44336; color: #c62828; font-family: Arial, sans-serif;">❌ MapLibre GL is not supported in this browser</div>';
          return;
        }
      } else {
        // Basic WebGL support check as fallback
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (!gl) {
          console.error('WebGL is not supported in this browser');
          this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #ffebee; border: 2px solid #f44336; color: #c62828; font-family: Arial, sans-serif;">❌ WebGL is not supported in this browser</div>';
          return;
        }
        console.log('WebGL support confirmed, proceeding with map initialization...');
      }

      // Clear the loading message
      this.el.innerHTML = '';

      // Get initial style
      const initialStyle = this.getMapStyle(this.currentStyle);

      // Initialize the map
      this.map = new maplibregl.Map({
        container: this.el,
        style: initialStyle,
        center: [center.lng, center.lat],
        zoom: zoom,
        attributionControl: true,
        logoPosition: 'bottom-left'
      });

      // Add navigation controls
      this.map.addControl(new maplibregl.NavigationControl(), 'top-right');

      // Add custom style switcher control
      this.map.addControl(new StyleSwitcherControl(this), 'top-left');

      // Create loading overlay
      this.createLoadingOverlay();

      // Handle map load event
      this.map.on('load', () => {
        console.log('MapLibre GL JS map loaded successfully');
        this.hideLoadingOverlay();
      });

      // Handle style loading events
      this.map.on('styledata', () => {
        console.log('Map style data loaded');
        this.hideLoadingOverlay();
      });

      // Handle map errors
      this.map.on('error', (e) => {
        console.error('MapLibre GL JS error:', e);
        this.hideLoadingOverlay();
      });

    } catch (error) {
      console.error('Error initializing MapLibre map:', error);
      this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #ffebee; border: 2px solid #f44336; color: #c62828; font-family: Arial, sans-serif;">❌ Error: ' + error.message + '</div>';
    }
  },

  // Get map style configuration
  getMapStyle(styleKey) {
    const styleConfig = MAP_STYLES[styleKey];
    if (!styleConfig) {
      console.warn(`Unknown style: ${styleKey}, falling back to standard`);
      return MAP_STYLES.standard.style;
    }

    return styleConfig.style;
  },

  // Switch map style
  switchStyle(newStyleKey) {
    if (!this.map || newStyleKey === this.currentStyle) {
      console.log(`Style switch skipped: map=${!!this.map}, newStyle=${newStyleKey}, currentStyle=${this.currentStyle}`);
      return;
    }

    console.log(`Switching map style from ${this.currentStyle} to ${newStyleKey}`);

    try {
      const newStyle = this.getMapStyle(newStyleKey);
      console.log('New style configuration:', newStyle);

      // Use MapLibre's setStyle method for smooth transitions
      this.map.setStyle(newStyle);

      // Update current style after successful switch
      this.currentStyle = newStyleKey;

      // Add event listener for style load to confirm success
      this.map.once('styledata', () => {
        console.log(`Successfully switched to ${newStyleKey} style`);
      });

      // Notify LiveView about style change
      this.pushEvent('style_changed', { style: newStyleKey });

    } catch (error) {
      console.error('Error switching map style:', error);
      // Revert button state if there was an error
      const buttons = document.querySelectorAll('.style-switcher-btn');
      buttons.forEach(btn => {
        btn.classList.toggle('active', btn.getAttribute('data-style') === this.currentStyle);
      });
    }
  },

  updated() {
    console.log('MapLibre hook updated');
  },

  destroyed() {
    console.log('MapLibre hook destroyed');
    if (this.map) {
      this.map.remove();
      this.map = null;
    }
  }
};

export default MapLibreHook;

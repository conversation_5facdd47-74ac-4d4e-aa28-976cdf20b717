// Import MapLibre GL JS - use require for better esbuild compatibility
const maplibregl = require('maplibre-gl');

// Define the MapLibre hook as a separate object to ensure proper registration
const MapLibreHook = {
  mounted() {
    console.log('MapLibre hook mounted successfully!');

    // Prevent double initialization
    if (this.map) {
      console.log('Map already initialized, skipping...');
      return;
    }

    // Show loading message
    this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #e8f5e8; border: 2px solid #4caf50; color: #2e7d32; font-family: Arial, sans-serif; font-size: 18px;">✅ MapLibre Hook Loaded!<br><small>Initializing map...</small></div>';

    // Initialize map immediately (no delay needed)
    this.initializeMap();
  },

  initializeMap() {
    try {
      // Prevent double initialization
      if (this.map) {
        console.log('Map already exists, skipping initialization...');
        return;
      }

      console.log('Initializing MapLibre map...');
      console.log('MapLibre GL object:', maplibregl);
      console.log('MapLibre GL keys:', Object.keys(maplibregl));

      // Try to get version from different possible locations
      const version = maplibregl.version || maplibregl.getVersion?.() || 'unknown';
      console.log('MapLibre GL version:', version);

      // Get configuration from data attributes
      const center = JSON.parse(this.el.dataset.center);
      const zoom = parseFloat(this.el.dataset.zoom);
      const style = this.el.dataset.style;

      console.log('Map config:', { center, zoom, style });

      // Check if MapLibre GL is supported (basic WebGL check)
      if (typeof maplibregl.supported === 'function') {
        if (!maplibregl.supported()) {
          console.error('MapLibre GL is not supported in this browser');
          this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #ffebee; border: 2px solid #f44336; color: #c62828; font-family: Arial, sans-serif;">❌ MapLibre GL is not supported in this browser</div>';
          return;
        }
      } else {
        // Basic WebGL support check as fallback
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (!gl) {
          console.error('WebGL is not supported in this browser');
          this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #ffebee; border: 2px solid #f44336; color: #c62828; font-family: Arial, sans-serif;">❌ WebGL is not supported in this browser</div>';
          return;
        }
        console.log('WebGL support confirmed, proceeding with map initialization...');
      }

      // Clear the loading message
      this.el.innerHTML = '';

      // Initialize the map
      this.map = new maplibregl.Map({
        container: this.el,
        style: style,
        center: [center.lng, center.lat],
        zoom: zoom,
        attributionControl: true,
        logoPosition: 'bottom-left'
      });

      // Add navigation controls
      this.map.addControl(new maplibregl.NavigationControl(), 'top-right');

      // Handle map load event
      this.map.on('load', () => {
        console.log('MapLibre GL JS map loaded successfully');
      });

      // Handle map errors
      this.map.on('error', (e) => {
        console.error('MapLibre GL JS error:', e);
      });

    } catch (error) {
      console.error('Error initializing MapLibre map:', error);
      this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #ffebee; border: 2px solid #f44336; color: #c62828; font-family: Arial, sans-serif;">❌ Error: ' + error.message + '</div>';
    }
  },

  updated() {
    console.log('MapLibre hook updated');
  },

  destroyed() {
    console.log('MapLibre hook destroyed');
    if (this.map) {
      this.map.remove();
      this.map = null;
    }
  }
};

export default MapLibreHook;
